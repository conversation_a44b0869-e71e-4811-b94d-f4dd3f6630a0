[2025-07-23 04:35:55] local.ERROR: Skipped encounter 26: No patient email {"id":"6","encounter_id":"26","medicine_name":"Co-amoxiclav","frequency":"three times daily","duration":"7","instruction":null,"added_by":"104","created_at":"2024-12-13 13:35:06","encounter_date":"2024-12-13","patient":{"id":"105","email":null,"name":null,"username":null,"first_name":null,"last_name":null,"phone":null,"role":"kiviCare_patient","registered_date":null},"doctor":{"id":"104","email":"<EMAIL>","name":"<PERSON><PERSON>","username":"Dr_<PERSON><PERSON><PERSON>_QE2APp","first_name":"<PERSON> <PERSON><PERSON>","last_name":"<PERSON>ech<PERSON>","phone":null,"role":"kivi<PERSON><PERSON>_doctor","registered_date":"2024-12-13 13:09:00"},"clinic":{"id":"13","name":"<PERSON><PERSON><PERSON>","email":"<EMAIL>","phone":"078283208208","address":"6 Scraptoft Mews","admin_email":"<EMAIL>","admin_name":"Moaize Chechi","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":null,"doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}} 
[2025-07-23 04:36:14] local.ERROR: Failed to migrate encounter 375: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'strength' cannot be null (Connection: mysql, SQL: insert into `prescription_items` (`prescription_id`, `medication_name`, `strength`, `form`, `dosage`, `frequency`, `route`, `quantity`, `quantity_unit`, `duration_days`, `directions_for_use`, `additional_instructions`, `status`, `updated_at`, `created_at`) values (1, Trimethoprim, ?, ?, ?, Twice daily, ?, ?, ?, 7, , , active, 2025-07-23 04:36:14, 2025-07-23 04:36:14)) [{"id":"135","encounter_id":"375","medicine_name":"Trimethoprim","frequency":"Twice daily","duration":"7","instruction":null,"added_by":"93","created_at":"2025-03-22 17:43:48","encounter_date":"2025-03-22","patient":{"id":"499","email":"<EMAIL>","name":"Jon Bowen","username":"Jon_sgC4Nw","first_name":"Jon","last_name":"Bowen","phone":null,"role":"kiviCare_patient","registered_date":"2025-03-22 17:31:44"},"doctor":{"id":"93","email":"<EMAIL>","name":"Asiyah Karam","username":"Asiyah_vuhTQf","first_name":"Asiyah","last_name":"Karam","phone":null,"role":"kiviCare_doctor","registered_date":"2024-12-09 13:53:24"},"clinic":{"id":"12","name":"Dr Asiyahs Clinic","email":"<EMAIL>","phone":"07500 114672","address":"Dandelion Health","admin_email":"<EMAIL>","admin_name":"Asiyah Karam","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 04:36:14] local.ERROR: Failed to migrate encounter 270: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'strength' cannot be null (Connection: mysql, SQL: insert into `prescription_items` (`prescription_id`, `medication_name`, `strength`, `form`, `dosage`, `frequency`, `route`, `quantity`, `quantity_unit`, `duration_days`, `directions_for_use`, `additional_instructions`, `status`, `updated_at`, `created_at`) values (2, Prednisolone enteric coated, ?, ?, ?, Once daily, ?, ?, ?, 5, to be taken with food, to be taken with food, active, 2025-07-23 04:36:14, 2025-07-23 04:36:14)) [{"id":"85","encounter_id":"270","medicine_name":"Prednisolone enteric coated","frequency":"Once daily","duration":"5","instruction":"to be taken with food","added_by":"93","created_at":"2025-03-06 16:10:57","encounter_date":"2025-03-06","patient":{"id":"408","email":"<EMAIL>","name":"Ellen May Thompson","username":"Ellen_May_25dMBj","first_name":"Ellen May","last_name":"Thompson","phone":null,"role":"kiviCare_patient","registered_date":"2025-03-06 11:14:48"},"doctor":{"id":"93","email":"<EMAIL>","name":"Asiyah Karam","username":"Asiyah_vuhTQf","first_name":"Asiyah","last_name":"Karam","phone":null,"role":"kiviCare_doctor","registered_date":"2024-12-09 13:53:24"},"clinic":{"id":"12","name":"Dr Asiyahs Clinic","email":"<EMAIL>","phone":"07500 114672","address":"Dandelion Health","admin_email":"<EMAIL>","admin_name":"Asiyah Karam","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 04:37:05] local.ERROR: Failed to migrate encounter 375: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 (Connection: mysql, SQL: insert into `prescription_items` (`prescription_id`, `medication_name`, `strength`, `form`, `dosage`, `frequency`, `route`, `quantity`, `quantity_unit`, `duration_days`, `directions_for_use`, `additional_instructions`, `status`, `updated_at`, `created_at`) values (1, Trimethoprim, Not specified, Not specified, As directed, Twice daily, oral, 1, units, 7, , , active, 2025-07-23 04:37:05, 2025-07-23 04:37:05)) [{"id":"135","encounter_id":"375","medicine_name":"Trimethoprim","frequency":"Twice daily","duration":"7","instruction":null,"added_by":"93","created_at":"2025-03-22 17:43:48","encounter_date":"2025-03-22","patient":{"id":"499","email":"<EMAIL>","name":"Jon Bowen","username":"Jon_sgC4Nw","first_name":"Jon","last_name":"Bowen","phone":null,"role":"kiviCare_patient","registered_date":"2025-03-22 17:31:44"},"doctor":{"id":"93","email":"<EMAIL>","name":"Asiyah Karam","username":"Asiyah_vuhTQf","first_name":"Asiyah","last_name":"Karam","phone":null,"role":"kiviCare_doctor","registered_date":"2024-12-09 13:53:24"},"clinic":{"id":"12","name":"Dr Asiyahs Clinic","email":"<EMAIL>","phone":"07500 114672","address":"Dandelion Health","admin_email":"<EMAIL>","admin_name":"Asiyah Karam","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 04:37:05] local.ERROR: Failed to migrate encounter 270: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 (Connection: mysql, SQL: insert into `prescription_items` (`prescription_id`, `medication_name`, `strength`, `form`, `dosage`, `frequency`, `route`, `quantity`, `quantity_unit`, `duration_days`, `directions_for_use`, `additional_instructions`, `status`, `updated_at`, `created_at`) values (2, Prednisolone enteric coated, Not specified, Not specified, As directed, Once daily, oral, 1, units, 5, to be taken with food, to be taken with food, active, 2025-07-23 04:37:05, 2025-07-23 04:37:05)) [{"id":"85","encounter_id":"270","medicine_name":"Prednisolone enteric coated","frequency":"Once daily","duration":"5","instruction":"to be taken with food","added_by":"93","created_at":"2025-03-06 16:10:57","encounter_date":"2025-03-06","patient":{"id":"408","email":"<EMAIL>","name":"Ellen May Thompson","username":"Ellen_May_25dMBj","first_name":"Ellen May","last_name":"Thompson","phone":null,"role":"kiviCare_patient","registered_date":"2025-03-06 11:14:48"},"doctor":{"id":"93","email":"<EMAIL>","name":"Asiyah Karam","username":"Asiyah_vuhTQf","first_name":"Asiyah","last_name":"Karam","phone":null,"role":"kiviCare_doctor","registered_date":"2024-12-09 13:53:24"},"clinic":{"id":"12","name":"Dr Asiyahs Clinic","email":"<EMAIL>","phone":"07500 114672","address":"Dandelion Health","admin_email":"<EMAIL>","admin_name":"Asiyah Karam","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 04:38:15] local.ERROR: Failed to migrate encounter 677: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 961, 2025-05-14 00:00:00, RX-68806737ccabb, active, ?, ?, 1, 959, 40, 2025-07-23 04:38:15, 2025-07-23 04:38:15)) [{"id":"298","encounter_id":"677","medicine_name":"amoxicillin Oral Capsule","frequency":"Three times daily","duration":"14","instruction":null,"added_by":"702","created_at":"2025-05-14 10:24:17","encounter_date":"2025-05-14","patient":{"id":"727","email":"<EMAIL>","name":"tester Patient","username":"tester_d7p2eU","first_name":"tester","last_name":"Patient","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-30 09:50:11"},"doctor":{"id":"702","email":"<EMAIL>","name":"Cuong Nguyen","username":"Cuong_lDWf08","first_name":"Cuong","last_name":"Nguyen","phone":null,"role":"kiviCare_doctor","registered_date":"2025-04-25 07:36:21"},"clinic":{"id":"40","name":"Dr Cuong Nguyen","email":"<EMAIL>","phone":"02076365150","address":"","admin_email":"<EMAIL>","admin_name":"Cuong Nguyen","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 04:38:15] local.ERROR: Failed to migrate encounter 611: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 961, 2025-04-30 00:00:00, RX-68806737d4d33, active, ?, ?, 1, 959, 40, 2025-07-23 04:38:15, 2025-07-23 04:38:15)) [{"id":"297","encounter_id":"611","medicine_name":"amoxicillin Oral Capsule","frequency":"Three times daily","duration":"14","instruction":null,"added_by":"696","created_at":"2025-05-14 09:44:52","encounter_date":"2025-04-30","patient":{"id":"727","email":"<EMAIL>","name":"tester Patient","username":"tester_d7p2eU","first_name":"tester","last_name":"Patient","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-30 09:50:11"},"doctor":{"id":"702","email":"<EMAIL>","name":"Cuong Nguyen","username":"Cuong_lDWf08","first_name":"Cuong","last_name":"Nguyen","phone":null,"role":"kiviCare_doctor","registered_date":"2025-04-25 07:36:21"},"clinic":{"id":"40","name":"Dr Cuong Nguyen","email":"<EMAIL>","phone":"02076365150","address":"","admin_email":"<EMAIL>","admin_name":"Cuong Nguyen","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 04:38:15] local.ERROR: Failed to migrate encounter 645: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 961, 2025-05-08 00:00:00, RX-68806737d73a1, active, ?, ?, 1, 959, 40, 2025-07-23 04:38:15, 2025-07-23 04:38:15)) [{"id":"283","encounter_id":"645","medicine_name":"amoxicillin Oral Capsule","frequency":"Three times daily","duration":"14","instruction":null,"added_by":"696","created_at":"2025-05-08 09:35:38","encounter_date":"2025-05-08","patient":{"id":"727","email":"<EMAIL>","name":"tester Patient","username":"tester_d7p2eU","first_name":"tester","last_name":"Patient","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-30 09:50:11"},"doctor":{"id":"702","email":"<EMAIL>","name":"Cuong Nguyen","username":"Cuong_lDWf08","first_name":"Cuong","last_name":"Nguyen","phone":null,"role":"kiviCare_doctor","registered_date":"2025-04-25 07:36:21"},"clinic":{"id":"40","name":"Dr Cuong Nguyen","email":"<EMAIL>","phone":"02076365150","address":"","admin_email":"<EMAIL>","admin_name":"Cuong Nguyen","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 04:38:15] local.ERROR: Failed to migrate encounter 644: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 960, 2025-05-07 00:00:00, RX-68806737da80c, active, ?, ?, 1, 959, 40, 2025-07-23 04:38:15, 2025-07-23 04:38:15)) [{"id":"282","encounter_id":"644","medicine_name":"amoxicillin Oral Capsule","frequency":"Three times daily","duration":"7","instruction":null,"added_by":"702","created_at":"2025-05-07 18:22:17","encounter_date":"2025-05-07","patient":{"id":"703","email":"<EMAIL>","name":"Test Patient","username":"Test_Wtyku2","first_name":"Test","last_name":"Patient","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-25 07:39:28"},"doctor":{"id":"702","email":"<EMAIL>","name":"Cuong Nguyen","username":"Cuong_lDWf08","first_name":"Cuong","last_name":"Nguyen","phone":null,"role":"kiviCare_doctor","registered_date":"2025-04-25 07:36:21"},"clinic":{"id":"40","name":"Dr Cuong Nguyen","email":"<EMAIL>","phone":"02076365150","address":"","admin_email":"<EMAIL>","admin_name":"Cuong Nguyen","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 04:38:15] local.ERROR: Failed to migrate encounter 609: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 961, 2025-04-30 00:00:00, RX-68806737dfb23, active, ?, ?, 2, 959, 40, 2025-07-23 04:38:15, 2025-07-23 04:38:15)) [{"id":"259","encounter_id":"609","medicine_name":"Flucloxin 500 MG Oral Capsule","frequency":"Every 6 hours","duration":"7","instruction":null,"added_by":"702","created_at":"2025-04-30 11:04:19","encounter_date":"2025-04-30","patient":{"id":"727","email":"<EMAIL>","name":"tester Patient","username":"tester_d7p2eU","first_name":"tester","last_name":"Patient","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-30 09:50:11"},"doctor":{"id":"702","email":"<EMAIL>","name":"Cuong Nguyen","username":"Cuong_lDWf08","first_name":"Cuong","last_name":"Nguyen","phone":null,"role":"kiviCare_doctor","registered_date":"2025-04-25 07:36:21"},"clinic":{"id":"40","name":"Dr Cuong Nguyen","email":"<EMAIL>","phone":"02076365150","address":"","admin_email":"<EMAIL>","admin_name":"Cuong Nguyen","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}},{"id":"260","encounter_id":"609","medicine_name":"amoxicillin Oral Tablet","frequency":"Three times daily","duration":"7","instruction":"with food","added_by":"702","created_at":"2025-04-30 11:04:19","encounter_date":"2025-04-30","patient":{"id":"727","email":"<EMAIL>","name":"tester Patient","username":"tester_d7p2eU","first_name":"tester","last_name":"Patient","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-30 09:50:11"},"doctor":{"id":"702","email":"<EMAIL>","name":"Cuong Nguyen","username":"Cuong_lDWf08","first_name":"Cuong","last_name":"Nguyen","phone":null,"role":"kiviCare_doctor","registered_date":"2025-04-25 07:36:21"},"clinic":{"id":"40","name":"Dr Cuong Nguyen","email":"<EMAIL>","phone":"02076365150","address":"","admin_email":"<EMAIL>","admin_name":"Cuong Nguyen","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 06:20:11] local.ERROR: Failed to migrate encounter 864: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_patient_id_foreign` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 1405, 2025-06-14 00:00:00, RX-68807f1b51030, active, ?, ?, 1, 992, 14, 2025-07-23 06:20:11, 2025-07-23 06:20:11)) [{"id":"385","encounter_id":"864","medicine_name":"Efudix Cream 5%","frequency":"Twice daily","duration":"28","instruction":"Apply twice daily for 1 month then stop","added_by":"224","created_at":"2025-06-14 11:50:14","encounter_date":"2025-06-14","patient":{"id":"889","email":"<EMAIL>","name":"Robert Coe","username":"Robert_XlzMRu","first_name":"Robert","last_name":"Coe","phone":null,"role":"kiviCare_patient","registered_date":"2025-06-06 10:31:09"},"doctor":{"id":"224","email":"<EMAIL>","name":"Skin Specialist GP","username":"GP_with_SI_in_ZJ452d","first_name":"Skin","last_name":"Specialist GP","phone":null,"role":"kiviCare_doctor","registered_date":"2025-01-27 11:15:54"},"clinic":{"id":"14","name":"Chelmsford Health Centre","email":"<EMAIL>","phone":"01245 823923","address":"Dickens Place","admin_email":"<EMAIL>","admin_name":"Poobashni Govender","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 06:20:12] local.ERROR: Failed to migrate encounter 661: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_patient_id_foreign` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 1400, 2025-05-11 00:00:00, RX-68807f1cc9ad0, active, ?, ?, 1, 997, 14, 2025-07-23 06:20:12, 2025-07-23 06:20:12)) [{"id":"291","encounter_id":"661","medicine_name":"Amoxicillin","frequency":"Three times daily","duration":"5","instruction":"one tablet three times a day for a total of 5 days","added_by":"215","created_at":"2025-05-11 10:34:46","encounter_date":"2025-05-11","patient":{"id":"769","email":"<EMAIL>","name":"Richard Cousins","username":"Richard_AsZ6mH","first_name":"Richard","last_name":"Cousins","phone":null,"role":"kiviCare_patient","registered_date":"2025-05-11 09:29:24"},"doctor":{"id":"215","email":"<EMAIL>","name":"Urgent Care Doctor","username":"Urgent_bUmxuN","first_name":"Urgent Care","last_name":"Doctor","phone":null,"role":"kiviCare_doctor","registered_date":"2025-01-24 13:51:05"},"clinic":{"id":"14","name":"Chelmsford Health Centre","email":"<EMAIL>","phone":"01245 823923","address":"Dickens Place","admin_email":"<EMAIL>","admin_name":"Poobashni Govender","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 06:20:13] local.ERROR: Failed to migrate encounter 517: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_patient_id_foreign` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 1393, 2025-04-15 00:00:00, RX-68807f1ddd4a8, active, ?, ?, 1, 988, 14, 2025-07-23 06:20:13, 2025-07-23 06:20:13)) [{"id":"213","encounter_id":"517","medicine_name":"Trimethoprim","frequency":"Twice daily","duration":"3","instruction":null,"added_by":"216","created_at":"2025-04-15 18:11:58","encounter_date":"2025-04-15","patient":{"id":"642","email":"<EMAIL>","name":"Ranee Edwards","username":"Ranee_8PIBjM","first_name":"Ranee","last_name":"Edwards","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-15 16:14:21"},"doctor":{"id":"216","email":"<EMAIL>","name":"Private GP","username":"Private_ktbC12","first_name":"Private","last_name":"GP","phone":null,"role":"kiviCare_doctor","registered_date":"2025-01-24 14:07:44"},"clinic":{"id":"14","name":"Chelmsford Health Centre","email":"<EMAIL>","phone":"01245 823923","address":"Dickens Place","admin_email":"<EMAIL>","admin_name":"Poobashni Govender","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 06:20:14] local.ERROR: Failed to migrate encounter 461: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_patient_id_foreign` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 1401, 2025-04-07 00:00:00, RX-68807f1e28e95, active, ?, ?, 2, 988, 14, 2025-07-23 06:20:14, 2025-07-23 06:20:14)) [{"id":"189","encounter_id":"461","medicine_name":"diclofenac MR","frequency":"Three times daily as required","duration":"7","instruction":"take with food and PPI","added_by":"216","created_at":"2025-04-07 17:49:40","encounter_date":"2025-04-07","patient":{"id":"591","email":"<EMAIL>","name":"Richard Keogh","username":"Richard_buA7iW","first_name":"Richard","last_name":"Keogh","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-07 15:33:57"},"doctor":{"id":"216","email":"<EMAIL>","name":"Private GP","username":"Private_ktbC12","first_name":"Private","last_name":"GP","phone":null,"role":"kiviCare_doctor","registered_date":"2025-01-24 14:07:44"},"clinic":{"id":"14","name":"Chelmsford Health Centre","email":"<EMAIL>","phone":"01245 823923","address":"Dickens Place","admin_email":"<EMAIL>","admin_name":"Poobashni Govender","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}},{"id":"190","encounter_id":"461","medicine_name":"nitrofurantoin MR","frequency":"Twice daily","duration":"7","instruction":null,"added_by":"216","created_at":"2025-04-07 17:49:40","encounter_date":"2025-04-07","patient":{"id":"591","email":"<EMAIL>","name":"Richard Keogh","username":"Richard_buA7iW","first_name":"Richard","last_name":"Keogh","phone":null,"role":"kiviCare_patient","registered_date":"2025-04-07 15:33:57"},"doctor":{"id":"216","email":"<EMAIL>","name":"Private GP","username":"Private_ktbC12","first_name":"Private","last_name":"GP","phone":null,"role":"kiviCare_doctor","registered_date":"2025-01-24 14:07:44"},"clinic":{"id":"14","name":"Chelmsford Health Centre","email":"<EMAIL>","phone":"01245 823923","address":"Dickens Place","admin_email":"<EMAIL>","admin_name":"Poobashni Govender","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 06:20:15] local.ERROR: Failed to migrate encounter 376: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_patient_id_foreign` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 1403, 2025-03-23 00:00:00, RX-68807f1f25db2, active, ?, ?, 1, 992, 14, 2025-07-23 06:20:15, 2025-07-23 06:20:15)) [{"id":"136","encounter_id":"376","medicine_name":"Efudex 5 % Topical Cream","frequency":"BD","duration":"1","instruction":"Apply twice a day for 1 month only","added_by":"224","created_at":"2025-03-23 10:13:45","encounter_date":"2025-03-23","patient":{"id":"474","email":"<EMAIL>","name":"Rita Bowkett-Smith","username":"Rita_nq9VTH","first_name":"Rita","last_name":"Bowkett-Smith","phone":null,"role":"kiviCare_patient","registered_date":"2025-03-18 10:10:47"},"doctor":{"id":"224","email":"<EMAIL>","name":"Skin Specialist GP","username":"GP_with_SI_in_ZJ452d","first_name":"Skin","last_name":"Specialist GP","phone":null,"role":"kiviCare_doctor","registered_date":"2025-01-27 11:15:54"},"clinic":{"id":"14","name":"Chelmsford Health Centre","email":"<EMAIL>","phone":"01245 823923","address":"Dickens Place","admin_email":"<EMAIL>","admin_name":"Poobashni Govender","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 06:20:15] local.ERROR: Failed to migrate encounter 364: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_patient_id_foreign` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 1395, 2025-03-21 00:00:00, RX-68807f1f42a21, active, ?, ?, 1, 997, 14, 2025-07-23 06:20:15, 2025-07-23 06:20:15)) [{"id":"131","encounter_id":"364","medicine_name":"flucloxacillin","frequency":"Every 6 hours","duration":"5","instruction":"Take if any signs of wound infection","added_by":"215","created_at":"2025-03-21 18:39:38","encounter_date":"2025-03-21","patient":{"id":"493","email":"<EMAIL>","name":"Reeta Basu","username":"Reeta_KSPfqL","first_name":"Reeta","last_name":"Basu","phone":null,"role":"kiviCare_patient","registered_date":"2025-03-21 16:30:22"},"doctor":{"id":"215","email":"<EMAIL>","name":"Urgent Care Doctor","username":"Urgent_bUmxuN","first_name":"Urgent Care","last_name":"Doctor","phone":null,"role":"kiviCare_doctor","registered_date":"2025-01-24 13:51:05"},"clinic":{"id":"14","name":"Chelmsford Health Centre","email":"<EMAIL>","phone":"01245 823923","address":"Dickens Place","admin_email":"<EMAIL>","admin_name":"Poobashni Govender","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 06:20:17] local.ERROR: Failed to migrate encounter 176: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`prescriptions`, CONSTRAINT `prescriptions_patient_id_foreign` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `prescriptions` (`consultation_id`, `patient_id`, `prescribed_date`, `prescription_number`, `status`, `clinical_indication`, `additional_instructions`, `total_items`, `prescriber_id`, `clinic_id`, `updated_at`, `created_at`) values (?, 1409, 2025-02-11 00:00:00, RX-68807f210e818, active, ?, ?, 1, 988, 14, 2025-07-23 06:20:17, 2025-07-23 06:20:17)) [{"id":"41","encounter_id":"176","medicine_name":"FLUCLOXACILLIN 500MG","frequency":"QDS","duration":"7","instruction":null,"added_by":"216","created_at":"2025-02-11 15:20:03","encounter_date":"2025-02-11","patient":{"id":"291","email":"<EMAIL>","name":"Roger Gibbard","username":"Roger_KVvZiJ","first_name":"Roger","last_name":"Gibbard","phone":null,"role":"kiviCare_patient","registered_date":"2025-02-11 14:45:39"},"doctor":{"id":"216","email":"<EMAIL>","name":"Private GP","username":"Private_ktbC12","first_name":"Private","last_name":"GP","phone":null,"role":"kiviCare_doctor","registered_date":"2025-01-24 14:07:44"},"clinic":{"id":"14","name":"Chelmsford Health Centre","email":"<EMAIL>","phone":"01245 823923","address":"Dickens Place","admin_email":"<EMAIL>","admin_name":"Poobashni Govender","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":"<EMAIL>","doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}}] 
[2025-07-23 06:20:22] local.ERROR: Skipped encounter 26: No patient email {"id":"6","encounter_id":"26","medicine_name":"Co-amoxiclav","frequency":"three times daily","duration":"7","instruction":null,"added_by":"104","created_at":"2024-12-13 13:35:06","encounter_date":"2024-12-13","patient":{"id":"105","email":null,"name":null,"username":null,"first_name":null,"last_name":null,"phone":null,"role":"kiviCare_patient","registered_date":null},"doctor":{"id":"104","email":"<EMAIL>","name":"Dr Moaize Chechi","username":"Dr_Moaize_QE2APp","first_name":"Dr Moaize","last_name":"Chechi","phone":null,"role":"kiviCare_doctor","registered_date":"2024-12-13 13:09:00"},"clinic":{"id":"13","name":"Doctor Chechi","email":"<EMAIL>","phone":"078283208208","address":"6 Scraptoft Mews","admin_email":"<EMAIL>","admin_name":"Moaize Chechi","admin_role":"kiviCare_clinic_admin"},"related_users":{"patient_email":null,"doctor_email":"<EMAIL>","clinic_admin_email":"<EMAIL>"}} 
[2025-07-23 06:32:45] local.ERROR: Skipped encounter 784: Patient record not found for user {"email":"<EMAIL>","user_id":1497} 
