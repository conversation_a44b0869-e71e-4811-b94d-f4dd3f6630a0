<?php

namespace App\Console\Commands\Migration;

use App\Models\Prescription;
use App\Models\PrescriptionItem;
use App\Models\Consultation;
use App\Models\Patient;
use App\Models\User;
use App\Services\Migration\DataTransformer;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Migrate Prescriptions Command
 * 
 * Simple focused migration for prescriptions only
 */
class MigratePrescriptions extends BaseMigrationCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migratewp:prescriptions
                            {--clinic= : Specific clinic ID, comma-separated IDs, or "all" for all clinics}
                            {--dry-run : Preview what would be migrated without executing}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate prescriptions from WordPress to Laravel';

    /**
     * Data transformer instance
     */
    protected $transformer;

    /**
     * Log channels
     */
    protected $logChannel;
    protected $errorLogChannel;

    public function __construct()
    {
        parent::__construct();
        $this->transformer = new DataTransformer();
        $this->setupLogging();
    }

    /**
     * Setup dedicated logging for prescriptions migration
     */
    protected function setupLogging()
    {
        $date = now()->format('Y_m_d');
        $logPath = storage_path("logs/migration/prescriptions");
        
        // Create directory if it doesn't exist
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }

        // Configure log channels
        config([
            'logging.channels.prescriptions_migration' => [
                'driver' => 'single',
                'path' => $logPath . "/prescriptions_{$date}.log",
                'level' => 'info',
            ],
            'logging.channels.prescriptions_errors' => [
                'driver' => 'single',
                'path' => $logPath . "/prescriptions_errors_{$date}.log",
                'level' => 'error',
            ]
        ]);

        $this->logChannel = 'prescriptions_migration';
        $this->errorLogChannel = 'prescriptions_errors';
    }

    /**
     * Execute the command
     */
    protected function executeCommand()
    {
        $this->info("=== MIGRATING PRESCRIPTIONS ===");
        $this->logInfo("Starting prescriptions migration");
        
        if ($this->isDryRun()) {
            $this->warn("DRY RUN MODE - Previewing migration without making changes");
            $this->logInfo("Running in DRY RUN mode");
        }

        $clinicOption = $this->option('clinic') ?? 'all';
        $clinicIds = $this->parseClinicOption($clinicOption);

        $totalProcessed = 0;
        $totalSkipped = 0;
        $totalErrors = 0;
        $failedClinics = [];

        foreach ($clinicIds as $clinicId) {
            $this->info("\n--- Migrating prescriptions for clinic ID: {$clinicId} ---");
            $this->logInfo("Starting prescriptions migration for clinic {$clinicId}");
            
            try {
                // 1. Make API call - get prescriptions for this clinic
                $response = $this->makeApiRequest('laravel_get_clinic_prescriptions', ['clinic_id' => $clinicId]);
                $wpPrescriptions = $response['data'] ?? [];

                if (empty($wpPrescriptions)) {
                    $this->info("No prescriptions found for clinic {$clinicId}");
                    $this->logInfo("No prescriptions found for clinic {$clinicId}");
                    continue;
                }

                $this->info("Found " . count($wpPrescriptions) . " prescriptions for clinic {$clinicId}");
                $this->logInfo("Found " . count($wpPrescriptions) . " prescriptions for clinic {$clinicId}");

                $processed = 0;
                $skipped = 0;
                $errors = 0;

                // 2. Group prescriptions by encounter_id to create proper prescription containers
                $prescriptionsByEncounter = [];
                foreach ($wpPrescriptions as $wpPrescription) {
                    $encounterId = $wpPrescription['encounter_id'] ?? 'no_encounter';
                    $prescriptionsByEncounter[$encounterId][] = $wpPrescription;
                }

                // 3. Process each encounter (prescription container)
                foreach ($prescriptionsByEncounter as $encounterId => $encounterPrescriptions) {
                    try {
                        // Use first prescription for encounter-level data
                        $firstPrescription = $encounterPrescriptions[0];

                        // Find patient by email
                        $patientEmail = $firstPrescription['related_users']['patient_email'] ?? null;
                        $prescriberEmail = $firstPrescription['related_users']['doctor_email'] ?? null;

                        if ($this->isDryRun()) {
                            $medicineCount = count($encounterPrescriptions);
                            $this->info("Would migrate encounter {$encounterId}: {$medicineCount} medicines for {$patientEmail} by {$prescriberEmail}");
                            $this->logInfo("DRY RUN: Would migrate encounter {$encounterId} with {$medicineCount} medicines");
                            $processed += $medicineCount;
                            continue;
                        }

                        if (!$patientEmail) {
                            $this->error("Skipped encounter {$encounterId}: No patient email");
                            $this->logError("Skipped encounter {$encounterId}: No patient email", $firstPrescription);
                            $skipped += count($encounterPrescriptions);
                            continue;
                        }

                        // Find user by email first
                        $user = User::where('email', $patientEmail)->first();
                        if (!$user) {
                            $this->error("Skipped encounter {$encounterId}: User not found ({$patientEmail})");
                            $this->logError("Skipped encounter {$encounterId}: User not found", ['email' => $patientEmail]);
                            $skipped += count($encounterPrescriptions);
                            continue;
                        }

                        // Find patient record for this user
                        $patient = \App\Models\Patient::where('user_id', $user->id)->first();
                        if (!$patient) {
                            $this->error("Skipped encounter {$encounterId}: Patient record not found for user ({$patientEmail})");
                            $this->logError("Skipped encounter {$encounterId}: Patient record not found for user", ['email' => $patientEmail, 'user_id' => $user->id]);
                            $skipped += count($encounterPrescriptions);
                            continue;
                        }

                        // Find prescriber by email
                        $prescriber = $prescriberEmail ? User::where('email', $prescriberEmail)->first() : null;

                        // Find related consultation if exists
                        $consultation = null;
                        if ($encounterId !== 'no_encounter') {
                            $consultation = Consultation::where('wp_encounter_id', $encounterId)->first();
                        }

                        // Create prescription container (one per encounter)
                        $laravelData = $this->transformer->transformPrescriptionSimple($firstPrescription);
                        $laravelData['patient_id'] = $patient->id;
                        $laravelData['prescriber_id'] = $prescriber ? $prescriber->id : null;
                        $laravelData['consultation_id'] = $consultation ? $consultation->id : null;

                        // Check if clinic exists in Laravel, set to null if not
                        $clinic = \App\Models\Clinic::find($clinicId);
                        $laravelData['clinic_id'] = $clinic ? $clinicId : null;
                        $laravelData['total_items'] = count($encounterPrescriptions);

                        // Use encounter_id as unique identifier for prescription container
                        $prescription = Prescription::updateOrCreate(
                            ['consultation_id' => $consultation ? $consultation->id : null, 'patient_id' => $patient->id, 'prescribed_date' => $laravelData['prescribed_date']],
                            $laravelData
                        );

                        // Create prescription items (one per medicine)
                        foreach ($encounterPrescriptions as $wpPrescription) {
                            $itemData = $this->transformer->transformPrescriptionItem($wpPrescription);
                            $itemData['prescription_id'] = $prescription->id;

                            PrescriptionItem::updateOrCreate(
                                ['prescription_id' => $prescription->id, 'medication_name' => $itemData['medication_name']],
                                $itemData
                            );
                        }

                        $medicineNames = array_column($encounterPrescriptions, 'medicine_name');
                        $this->info("✓ Migrated prescription: {$prescription->prescription_number} with " . count($encounterPrescriptions) . " medicines: " . implode(', ', $medicineNames));
                        $this->logInfo("Successfully migrated encounter {$encounterId} → Laravel prescription ID {$prescription->id} with " . count($encounterPrescriptions) . " items");
                        $processed += count($encounterPrescriptions);

                    } catch (Exception $e) {
                        $this->error("✗ Failed to migrate encounter {$encounterId}: " . $e->getMessage());
                        $this->logError("Failed to migrate encounter {$encounterId}: " . $e->getMessage(), $encounterPrescriptions);
                        $errors += count($encounterPrescriptions);
                    }
                }

                $this->info("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
                $this->logInfo("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");

                $totalProcessed += $processed;
                $totalSkipped += $skipped;
                $totalErrors += $errors;

            } catch (Exception $e) {
                $this->error("❌ Failed to process clinic {$clinicId}: " . $e->getMessage());
                $this->logError("Failed to process clinic {$clinicId}: " . $e->getMessage());
                $failedClinics[] = $clinicId;
                continue;
            }
        }

        // 3. Generate summary
        $this->generateSummary($totalProcessed, $totalSkipped, $totalErrors, $failedClinics);

        return 0;
    }

    /**
     * Generate migration summary
     */
    protected function generateSummary($processed, $skipped, $errors, $failedClinics)
    {
        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 PRESCRIPTIONS MIGRATION SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("✅ Processed: {$processed}");
        $this->info("⏭️  Skipped: {$skipped}");
        $this->info("❌ Errors: {$errors}");
        
        if (!empty($failedClinics)) {
            $this->error("🚨 Failed Clinics: " . implode(', ', $failedClinics));
        }
        
        if ($errors > 0 || !empty($failedClinics)) {
            $this->error("\n⚠️  Some prescriptions failed to migrate. Check error logs for details.");
        } else {
            $this->info("\n🎉 All prescriptions migrated successfully!");
        }
        
        $this->info("📁 Logs saved to: storage/logs/migration/prescriptions/");
        $this->info(str_repeat("=", 60));

        // Log summary
        $this->logInfo("Migration completed - Processed: {$processed}, Skipped: {$skipped}, Errors: {$errors}");
        if (!empty($failedClinics)) {
            $this->logError("Failed clinics: " . implode(', ', $failedClinics));
        }
    }

    /**
     * Log info message
     */
    protected function logInfo($message, $context = [])
    {
        Log::channel($this->logChannel)->info($message, $context);
    }

    /**
     * Log error message
     */
    protected function logError($message, $context = [])
    {
        Log::channel($this->errorLogChannel)->error($message, $context);
    }
}
